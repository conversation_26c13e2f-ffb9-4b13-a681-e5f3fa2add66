package pt.jumia.services.brad.domain.usecases.bale.brad;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.error.BaleProcessingErrorResult;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.UpdateExecutionLogsUseCase;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SyncBradBaleUseCaseTest {

    private static final List<Bale> BALE_LIST = new ArrayList<>(FakeBale.getFakeBale(10));
    
    @Mock
    private BradBaleRepository bradBaleRepository;

    @Mock
    private ReadCurrenciesUseCase readCurrenciesUseCase;

    @Mock
    private ReadAccountsUseCase readAccountsUseCase;

    @Mock
    private ReadBradFxRateUseCase readBradFxRateUseCase;

    @Mock
    private UpdateExecutionLogsUseCase updateExecutionLogsUseCase;



    @InjectMocks
    private SyncBradBaleUseCase syncBradBaleUseCase;

    private ExecutionLog executionLog;
    private List<Bale> testBales;
    private Account testAccount;
    private Currency testCurrency;

    @BeforeEach
    void setUp() {
        executionLog = ExecutionLog.builder()
                .id(1L)
                .logType(ExecutionLog.ExecutionLogType.BALE)
                .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
                .build();
        
        testAccount = FakeAccounts.getFakeAccounts(1, null).get(0);
        testCurrency = FakeCurrencies.ALL_CURRENCIES.get(0);
        testBales = FakeBale.getFakeBale(3);


        
        lenient().when(updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(anyList())).thenReturn(List.of());
        lenient().when(updateExecutionLogsUseCase.determineExecutionLogStatus(anyInt(), anyInt(), anyBoolean()))
                .thenReturn(ExecutionLog.ExecutionLogStatus.SYNCED);
    }

    @Test
    public void syncBradBale_success() throws NotFoundException, DatabaseErrorsException {

        when(readCurrenciesUseCase.execute("NGN")).thenReturn(FakeCurrencies.NGN);
        when(readAccountsUseCase.executeByNavReference(FakeAccounts.FAKE_ACCOUNT.getNavReference())).thenReturn(FakeAccounts.FAKE_ACCOUNT);

        when(bradBaleRepository.sync(any(List.class))).thenReturn(BALE_LIST);
        
        when(updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(any())).thenReturn(List.of());
        when(updateExecutionLogsUseCase.determineExecutionLogStatus(anyInt(), anyInt(), anyBoolean()))
                .thenReturn(ExecutionLog.ExecutionLogStatus.SYNCED);

        List<Bale> bales = syncBradBaleUseCase.execute(BALE_LIST, ExecutionLog.builder().build());

        assertEquals(BALE_LIST, bales);
        verify(bradBaleRepository).sync(any(List.class));
    }

    @Test
    void shouldHandleCurrencyLookupFailuresGracefully() throws Exception {
        when(readAccountsUseCase.executeByNavReference(anyString())).thenReturn(testAccount);
        
        when(readCurrenciesUseCase.execute(anyString()))
                .thenThrow(new NotFoundException("Currency not found"))
                .thenReturn(testCurrency)
                .thenReturn(testCurrency);
        
        when(bradBaleRepository.sync(any())).thenReturn(testBales);
        
        List<Bale> result = syncBradBaleUseCase.execute(testBales, executionLog);
        
        assertEquals(3, result.size(), "Should process all bales successfully using fallback currency");
        
        verify(updateExecutionLogsUseCase).updateExecutionLogWithSuccess(executionLog);
    }

    @Test
    void shouldReturnPartialSuccessWithMixedResults() throws Exception {
        Bale bale1 = testBales.get(0);
        Bale bale2 = testBales.get(1);
        Bale bale3 = testBales.get(2);
        
        when(readAccountsUseCase.executeByNavReference(anyString())).thenReturn(testAccount);
        
        when(readCurrenciesUseCase.execute(bale1.getTransactionCurrency().getCode())).thenReturn(testCurrency);
        when(readCurrenciesUseCase.execute(bale2.getTransactionCurrency().getCode())).thenReturn(testAccount.getCurrency());
        when(readCurrenciesUseCase.execute(bale3.getTransactionCurrency().getCode()))
                .thenThrow(new NotFoundException("Currency not found"));
        
        List<Bale> processedBales = List.of(bale1, bale2);
        when(bradBaleRepository.sync(any())).thenReturn(processedBales);
        
        List<Bale> result = syncBradBaleUseCase.execute(testBales, executionLog);
        
        assertEquals(2, result.size(), "Should return successfully processed bales");
    }

    @Test
    void shouldLogDetailedErrorContextForRecoverableErrors() throws Exception {
        List<Bale> singleBale = List.of(testBales.get(0));
        
        when(readAccountsUseCase.executeByNavReference(anyString()))
                .thenThrow(new NotFoundException("Account with NAV Reference 'ACC123' not found"));
        
        List<Bale> result = syncBradBaleUseCase.execute(singleBale, executionLog);
        
        assertTrue(result.isEmpty(), "Should return empty list when no accounts found");
        
        ArgumentCaptor<List<BaleProcessingErrorResult>> errorsCaptor = ArgumentCaptor.forClass(List.class);
        verify(updateExecutionLogsUseCase).convertBaleErrorsToSyncingErrors(errorsCaptor.capture());
        
        List<BaleProcessingErrorResult> capturedErrors = errorsCaptor.getValue();
        assertFalse(capturedErrors.isEmpty(), "Should have captured error details");
        
        BaleProcessingErrorResult error = capturedErrors.get(0);
        assertEquals("Account Lookup", error.getOperationContext());
        assertNotNull(error.getAccountNumber());
        assertNotNull(error.getEntryNo());
    }

    @Test
    void shouldHandleRepositorySyncFailureGracefully() throws Exception {
        when(readAccountsUseCase.executeByNavReference(anyString())).thenReturn(testAccount);
        when(readCurrenciesUseCase.execute(anyString())).thenReturn(testCurrency);
        
        when(bradBaleRepository.sync(any()))
                .thenThrow(new RuntimeException("Validation error in bale data"));
        
        List<Bale> result = syncBradBaleUseCase.execute(testBales, executionLog);
        
        assertTrue(result.isEmpty(), "Should return empty list when repository sync fails");
        
        verify(updateExecutionLogsUseCase).updateExecutionLogWithError(eq(executionLog), any());
    }

    @Test
    void shouldHandleCriticalRepositoryError() throws Exception {
        when(readAccountsUseCase.executeByNavReference(anyString())).thenReturn(testAccount);
        when(readCurrenciesUseCase.execute(anyString())).thenReturn(testCurrency);
        
        when(bradBaleRepository.sync(any()))
                .thenThrow(new RuntimeException("Database connection lost", 
                          new SQLException("Database connection lost")));
        
        List<Bale> result = syncBradBaleUseCase.execute(testBales, executionLog);
        
        assertTrue(result.isEmpty(), "Should return empty list on critical repository error");
        
        verify(updateExecutionLogsUseCase).updateExecutionLogWithError(eq(executionLog), any());
    }

    @Test
    void shouldHandleEmptyBaleList() throws Exception {
        List<Bale> emptyList = List.of();
        
        List<Bale> result = syncBradBaleUseCase.execute(emptyList, executionLog);
        
        assertTrue(result.isEmpty(), "Should return empty list for empty input");
        
        verify(readAccountsUseCase, never()).executeByNavReference(anyString());
        verify(readCurrenciesUseCase, never()).execute(anyString());
        verify(bradBaleRepository, never()).sync(any());
    }

}
