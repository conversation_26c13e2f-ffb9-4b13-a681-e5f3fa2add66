package pt.jumia.services.brad.domain.usecases.bale;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;

import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;

import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.UpdateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class ReadBaleUseCase {

    private final BaleRepository baleRepository;
    private final BradBaleRepository bradBaleRepository;

    private final SyncBradBaleUseCase syncBradBaleUseCase;
    private final CreateExecutionLogsUseCase createExecutionLogsUseCase;
    private final UpdateExecutionLogsUseCase updateExecutionLogsUseCase;
    private final ReadExecutionLogsUseCase readExecutionLogsUseCase;
    private final ReadViewEntityUseCase readViewEntityUseCase;


    public List<Bale> execute(List<ViewEntity> baleViewEntities) throws DatabaseErrorsException, EntityErrorsException {
        log.info("Starting bale processing for {} view entities", baleViewEntities.size());

        try {
            List<ViewEntity> viewsToProcess = baleViewEntities.isEmpty() ?
                readViewEntityUseCase.execute(ViewEntity.EntityType.BALE) : baleViewEntities;

            log.info("Processing {} view entities", viewsToProcess.size());
            return fetchAllBales(viewsToProcess, null);

        } catch (Exception e) {
            log.error("Error in bale processing: {}", e.getMessage(), e);
            throw new RuntimeException("Bale processing failed", e);
        }
    }

    public Integer executeLastBaleOfOffset(Long baleViewEntityId, Integer offset)
            throws NotFoundException, EntityErrorsException {
        ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        return baleRepository.findLastBaleWithOffset(baleViewEntity, offset);
    }

    public List<Bale> executeAllLastBaleOfOffset(Long baleViewEntityId, Integer offset)
            throws NotFoundException, EntityErrorsException {
        ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        return baleRepository.findAllLastBaleWithOffset(baleViewEntity, offset);
    }

    public void executeByEntryNo(Integer entryNo) {
        CompletableFuture.runAsync(() -> {
            try {
                this.fetchAllBales(List.of(), entryNo);
            } catch (EntityErrorsException e) {
                log.error("Bale sync: Error fetching bales by entry no: {} Exception: {}", entryNo, ExceptionUtils.getStackTrace(e));
            }
        });
    }

    public void executeWithEntryNoInBaleView(Integer entryNo, Long baleViewEntityId) {
        CompletableFuture.runAsync(() -> {
            try {
                ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);
                this.fetchAllBales(List.of(baleViewEntity), entryNo);
            } catch (EntityErrorsException | NotFoundException e) {
                log.error("Bale sync: Error fetching bales by entry no in bale view: {}", ExceptionUtils.getStackTrace(e));
            }
            });
    }

    public void executeWithBaleViewIds(List<Integer> baleViewIds) {
        CompletableFuture.runAsync(() -> {
            try {
                List<ViewEntity> baleViewEntities = baleViewIds.stream().map(id -> {
                    try {
                        return readViewEntityUseCase.execute(id, ViewEntity.EntityType.BALE);
                    } catch (NotFoundException e) {
                        log.error("Bale sync: Bale view entity with id {} not found. Exception: {}", id, ExceptionUtils.getStackTrace(e));
                        throw new RuntimeException(e);
                    }
                }).collect(Collectors.toList());

                if (baleViewEntities.isEmpty()) {
                    log.info("Bale sync: No bale view entities found for the provided bale view ids: {}", baleViewIds);
                    return;
                }

                this.fetchAllBales(baleViewEntities, null);
            } catch (EntityErrorsException e) {
                log.error("Bale sync: Error fetching bales by bale view ids. Exception: {}", ExceptionUtils.getStackTrace(e));
            }
        });
    }







    private List<Bale> fetchAllBales(List<ViewEntity> baleViewEntities, Integer startEntryNo) throws EntityErrorsException {
        log.debug("Bale sync: Starting bale synchronization process for Bale view entities provided: {}", baleViewEntities);

        if (baleViewEntities.isEmpty()) {
            log.info("Bale sync: No bale view entities provided, fetching all bale view entities.");
            baleViewEntities = readViewEntityUseCase.execute(ViewEntity.EntityType.BALE);
        }

        log.info("Bale sync: {} bale view entities found. Details: {}", baleViewEntities.size(), baleViewEntities);
        List<Bale> totalBales = new ArrayList<>();

        baleViewEntities.forEach(baleViewEntity -> {
            String companyId = null;
            try {
                log.info("Bale sync: Fetching company for the Bale view: {}", baleViewEntity.getViewName());
                companyId = baleRepository.fetchCompanyId(baleViewEntity, false);
                log.debug("Bale sync: Creating partition for company: {}, for bale view entity: {}", companyId, baleViewEntity);
                this.bradBaleRepository.createPartition(companyId);
            } catch (Exception ignored) {
                log.warn("Bale sync: Ignoring company creation");
            }
            try {
                ExecutionLog newExecutionLog = ExecutionLog.builder()
                        .logType(ExecutionLog.ExecutionLogType.BALE)
                        .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
                        .build();
                ExecutionLog createdExecutionLog = createExecutionLogsUseCase.execute(newExecutionLog);
                log.info("Bale sync: Execution log created: {}", createdExecutionLog.getId());
                Optional<Bale> lastBale = bradBaleRepository.findLastBaleInBradOfCompanyId(companyId);
                Integer entryNo = Objects.isNull(startEntryNo) ?
                        lastBale.map(Bale::getEntryNo).orElse(null) :
                        startEntryNo;
                log.info("Bale sync: execution id {} : First entry number: {}. Fetching bale list.",
                        createdExecutionLog.getId(), entryNo);

                List<Bale> baleList = new ArrayList<>(baleRepository.findAll(entryNo,
                        baleViewEntity,
                        false,
                        createdExecutionLog));

                if (baleList.isEmpty()) {
                    log.info("Bale sync: execution id {}: Bale list is empty.", createdExecutionLog.getId());
                    try {
                        ExecutionLog executionLog = readExecutionLogsUseCase.execute(createdExecutionLog.getId());
                        updateExecutionLogsUseCase.execute(executionLog.toBuilder()
                                .logStatus(ExecutionLog.ExecutionLogStatus.EMPTY)
                                .build());
                    } catch (Exception e) {
                        log.info("Bale sync: Error updating execution log: {}", ExceptionUtils.getStackTrace(e));
                    }
                } else {
                    log.info("Bale sync: execution id {}: Processing Bale List - total items: {} Details: {}",
                            createdExecutionLog.getId(), baleList.size(), baleList);
                    ExecutionLog updatedExecutionLog = readExecutionLogsUseCase.execute(createdExecutionLog.getId());
                    totalBales.addAll(syncBradBaleUseCase.execute(baleList, updatedExecutionLog));
                }

            } catch (DatabaseErrorsException | EntityErrorsException | ParseException e) {
                log.error("Bale sync: Error fetching bales: {}", ExceptionUtils.getStackTrace(e));
                throw new RuntimeException(e);
            } catch (NotFoundException e) {
                log.error("Bale sync: Error creating execution log: {}", ExceptionUtils.getStackTrace(e));
            }
        });
        log.info("Bale sync: Total bales processed: {}. Details: {}", totalBales.size(), totalBales);
        return totalBales;
    }


}
