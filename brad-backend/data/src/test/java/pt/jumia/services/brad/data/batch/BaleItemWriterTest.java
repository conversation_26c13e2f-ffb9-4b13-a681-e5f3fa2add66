package pt.jumia.services.brad.data.batch;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.item.Chunk;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCaseSimplified;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit test for BaleItemWriter to verify Spring Batch item writing.
 * 
 * This test ensures that the Spring Batch item writer correctly delegates
 * to the simplified use case and handles chunks appropriately.
 */
@ExtendWith(MockitoExtension.class)
class BaleItemWriterTest {

    @Mock
    private SyncBradBaleUseCaseSimplified syncBradBaleUseCase;

    private BaleItemWriter writer;

    @BeforeEach
    void setUp() {
        writer = new BaleItemWriter(syncBradBaleUseCase);
    }

    @Test
    void write_WithValidChunk_ShouldDelegateToUseCase() throws Exception {
        // Given
        List<Bale> bales = FakeBale.getFakeBale(3);
        Chunk<Bale> chunk = new Chunk<>(bales);
        
        when(syncBradBaleUseCase.execute(bales)).thenReturn(bales);

        // When
        writer.write(chunk);

        // Then
        verify(syncBradBaleUseCase).execute(bales);
    }

    @Test
    void write_WithEmptyChunk_ShouldNotCallUseCase() throws Exception {
        // Given
        Chunk<Bale> emptyChunk = new Chunk<>();

        // When
        writer.write(emptyChunk);

        // Then
        verify(syncBradBaleUseCase, never()).execute(any());
    }

    @Test
    void write_WithLargeChunk_ShouldHandleEfficiently() throws Exception {
        // Given
        List<Bale> largeBaleList = FakeBale.getFakeBale(1000);
        Chunk<Bale> largeChunk = new Chunk<>(largeBaleList);
        
        when(syncBradBaleUseCase.execute(largeBaleList)).thenReturn(largeBaleList);

        // When
        assertDoesNotThrow(() -> writer.write(largeChunk));

        // Then
        verify(syncBradBaleUseCase).execute(largeBaleList);
    }

    @Test
    void springBatchIntegration_ShouldReplaceCustomWriting() {
        // This test verifies that Spring Batch item writing replaces the
        // over-engineered custom writing logic that was removed during refactoring
        
        assertNotNull(writer, "BaleItemWriter should be created successfully");
        
        // Verify that the writer follows Spring Batch patterns
        assertTrue(writer instanceof org.springframework.batch.item.ItemWriter,
                "Should implement Spring Batch ItemWriter interface");
        
        // Verify that it delegates to simplified use case instead of complex custom logic
        assertDoesNotThrow(() -> {
            List<Bale> testBales = FakeBale.getFakeBale(5);
            Chunk<Bale> testChunk = new Chunk<>(testBales);
            
            when(syncBradBaleUseCase.execute(testBales)).thenReturn(testBales);
            
            writer.write(testChunk);
            
            verify(syncBradBaleUseCase).execute(testBales);
        }, "Should delegate to simplified use case without complex custom writing logic");
    }

    @Test
    void write_WithPartialSuccess_ShouldLogWarning() throws Exception {
        // Given
        List<Bale> inputBales = FakeBale.getFakeBale(5);
        List<Bale> outputBales = FakeBale.getFakeBale(3); // Fewer than input
        Chunk<Bale> chunk = new Chunk<>(inputBales);
        
        when(syncBradBaleUseCase.execute(inputBales)).thenReturn(outputBales);

        // When
        assertDoesNotThrow(() -> writer.write(chunk));

        // Then
        verify(syncBradBaleUseCase).execute(inputBales);
        // The warning should be logged (verified by the implementation)
    }
}
